<?php

require 'vendor/autoload.php'; // Nạ<PERSON> thư viện Faker PHP

class RandomUser
{
    private $faker;

    public function __construct()
    {
        // Khởi tạo Faker với dữ liệu giả định dạng Mỹ
        $this->faker = Faker\Factory::create('en_US');
    }

    /**
     * L<PERSON>y dữ liệu người dùng ngẫu nhiên từ Faker
     *
     * @return array|null
     */
    public function getUserFromUS()
    {
        try {
            // Sử dụng Faker để tạo thông tin người dùng
            $firstname = $this->faker->firstName;
            $lastname = $this->faker->lastName;
            $email = $this->generateRandomEmail($firstname, $lastname);

            // Lấy địa chỉ
            $street = $this->faker->streetAddress;
            $city = $this->faker->city;
            $postcode = $this->faker->postcode;
            $stateFullName = $this->faker->state;
            $stateAbbreviation = $this->getStateAbbreviation($stateFullName);

            // Lấy số điện thoại
            $phone = $this->faker->phoneNumber;

            // Trả về tất cả thông tin dưới dạng mảng
            return [
                'firstname' => $firstname,
                'lastname' => $lastname,
                'email' => $email,
                'street' => $street,
                'city' => $city,
                'postcode' => $postcode,
                'state_full' => $stateFullName,
                'state_abbreviation' => $stateAbbreviation,
                'phone' => $phone
            ];
        } catch (Exception $e) {
            // Trả về lỗi với thông tin từ $e
            die(json_encode([
                'status' => 'unk',
                'message' => $e->getMessage()
            ]));
        }
    }

    /**
     * Chuyển đổi tên đầy đủ của tiểu bang sang viết tắt
     *
     * @param string $stateFullName
     * @return string|null
     */
    private function getStateAbbreviation($stateFullName)
    {
        $states = [
            'Alabama' => 'AL',
            'Alaska' => 'AK',
            'Arizona' => 'AZ',
            'Arkansas' => 'AR',
            'California' => 'CA',
            'Colorado' => 'CO',
            'Connecticut' => 'CT',
            'Delaware' => 'DE',
            'Florida' => 'FL',
            'Georgia' => 'GA',
            'Hawaii' => 'HI',
            'Idaho' => 'ID',
            'Illinois' => 'IL',
            'Indiana' => 'IN',
            'Iowa' => 'IA',
            'Kansas' => 'KS',
            'Kentucky' => 'KY',
            'Louisiana' => 'LA',
            'Maine' => 'ME',
            'Maryland' => 'MD',
            'Massachusetts' => 'MA',
            'Michigan' => 'MI',
            'Minnesota' => 'MN',
            'Mississippi' => 'MS',
            'Missouri' => 'MO',
            'Montana' => 'MT',
            'Nebraska' => 'NE',
            'Nevada' => 'NV',
            'New Hampshire' => 'NH',
            'New Jersey' => 'NJ',
            'New Mexico' => 'NM',
            'New York' => 'NY',
            'North Carolina' => 'NC',
            'North Dakota' => 'ND',
            'Ohio' => 'OH',
            'Oklahoma' => 'OK',
            'Oregon' => 'OR',
            'Pennsylvania' => 'PA',
            'Rhode Island' => 'RI',
            'South Carolina' => 'SC',
            'South Dakota' => 'SD',
            'Tennessee' => 'TN',
            'Texas' => 'TX',
            'Utah' => 'UT',
            'Vermont' => 'VT',
            'Virginia' => 'VA',
            'Washington' => 'WA',
            'West Virginia' => 'WV',
            'Wisconsin' => 'WI',
            'Wyoming' => 'WY',
        ];

        return $states[$stateFullName] ?? null; // Trả về viết tắt hoặc null nếu không tìm thấy
    }

    /**
     * Tạo email ngẫu nhiên từ họ và tên
     *
     * @param string $firstName
     * @param string $lastName
     * @return string
     */
    private function generateRandomEmail($firstName, $lastName)
    {
        // Các domain email phổ biến
        $domains = ['gmail.com', 'hotmail.com', 'yahoo.com', 'outlook.com'];
        // Random domain
        $domain = $domains[array_rand($domains)];
        // Tạo email ngẫu nhiên từ họ, tên và domain
        $email = strtolower($firstName . '.' . $lastName . rand(10000, 99999) . '@' . $domain);

        return $email;
    }
}