<?php

class MailTM {
    private $apiUrl = 'https://api.mail.tm'; // Đúng endpoint API
    private $token;
    private $account;

    // Hàm lấy mã xác minh từ tin nhắn
    private $utility;

    public function __construct() {
        // Khởi tạo Utility
        $this->utility = new Utility();
    }

    


    // Lấy danh sách các domain có sẵn
    public function getDomains() {
        $url = $this->apiUrl . '/domains';
        $response = $this->makeRequest('GET', $url);
        return $response['hydra:member'];
    }

    // Tạo tài khoản email mới
    public function createAccount($email, $password) {
        $url = $this->apiUrl . '/accounts';
        $data = [
            'address' => $email,
            'password' => $password,
        ];
        $response = $this->makeRequest('POST', $url, $data);
        $this->account = $response;
        return $response;
    }

    // Đăng nhập và lấy token
    public function login($email, $password) {
        $url = $this->apiUrl . '/token'; // Endpoint đúng để lấy token
        $data = [
            'address' => $email,
            'password' => $password,
        ];

        // Gửi yêu cầu POST tới API để lấy token
        $response = $this->makeRequest('POST', $url, $data);

        // Kiểm tra nếu phản hồi có lỗi
        if (!$response || isset($response['error'])) {
            // echo 'Error in login response: ';
            // print_r($response);
            return null;
        }

        // Lấy token nếu tồn tại
        if (isset($response['token'])) {
            $this->token = $response['token'];
            return $this->token;
        } else {
            echo 'Login failed. Response: ';
            print_r($response); // In ra phản hồi nếu thất bại
            return null;
        }
    }

    // Hàm lấy tin nhắn với vòng lặp kiểm tra mỗi 2 giây, timeout sau 14 giây
    public function getMessagesWithTimeout($timeout = 14) {
        $url = $this->apiUrl . '/messages';
        $elapsedTime = 0;
        $interval = 2;  // Kiểm tra mỗi 2 giây
    
        while ($elapsedTime < $timeout) {
            $response = $this->makeRequest('GET', $url, [], true);
            $messages = $response['hydra:member'];
    
            if (!empty($messages)) {
                // Duyệt qua từng tin nhắn và echo ra id
                foreach ($messages as $message) {
                    // echo 'Message ID: ' . $message['id'] . '<br>';
                }
                return $messages;
            }
    
            sleep($interval);  // Đợi 2 giây trước khi thử lại
            $elapsedTime += $interval;
        }
    
        // Nếu sau 14 giây không có tin nhắn, trả về trạng thái lỗi
        die('Error: No messages found.');
    }

    // Xem nội dung chi tiết của tin nhắn
    public function getMessage($messageId) {
        $url = $this->apiUrl . '/messages/' . $messageId;
        $response = $this->makeRequest('GET', $url, [], true);
        
        // Trích xuất mã xác minh gồm 6 số từ text của tin nhắn
        $verificationCode = $this->extractVerificationCode($response['text']);
        
        if ($verificationCode) {
            // echo 'Verification Code: ' . $verificationCode . '<br>';
            return $verificationCode;  // Trả về mã xác minh nếu tìm thấy
        } else {
            die(json_encode(['status' => 'unk', 'message' => 'Verification code not found']));
            return null;  // Trả về null nếu không tìm thấy
        }
    }
    
    public function extractVerificationCode($text) {
        $start = 'verification code below:';
        $end = 'This code';
        
        $startPos = strpos($text, $start);
        $endPos = strpos($text, $end, $startPos);
        
        if ($startPos !== false && $endPos !== false) {
            $startPos += strlen($start);
            $verificationCode = substr($text, $startPos, $endPos - $startPos);
            
            $verificationCode = trim($verificationCode);
            if (preg_match('/\d{6}/', $verificationCode, $matches)) {
                return $matches[0];
            }
        }
    
        return null;
    }

    // Hàm thực hiện request
    private function makeRequest($method, $url, $data = [], $auth = false) {
        $ch = curl_init();
        $headers = ['Content-Type: application/json'];
        if ($auth && $this->token) {
            $headers[] = 'Authorization: Bearer ' . $this->token;
        }

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        if (!empty($data)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }

    // Sinh chuỗi ngẫu nhiên
    public function generateRandomString($length = 8) {
        return substr(str_shuffle("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"), 0, $length);
    }

    public function generateRandomEmail($domain) {
        return $this->generateRandomString() . '@' . $domain;
    }

    public function generateRandomPassword($length = 12) {
        return $this->generateRandomString($length);
    }
}