<?php

require 'vendor/autoload.php';

use GuzzleHttp\Client;
use Dotenv\Dotenv;


class Telegram
{
    private $botToken;
    private $chatId;
    private $enabledTelegram;

    public function __construct()
    {
        require_once(__DIR__ . "/../../../../../config.php");
        require_once(__DIR__ . "/../../../../../libs/db.php");
        require_once(__DIR__ . "/../../../../../libs/lang.php");
        require_once(__DIR__ . "/../../../../../libs/helper.php");
        require_once(__DIR__ . '/../../../../../libs/database/users.php');

        require_once(__DIR__ . "/../../../../../models/is_user.php");
        $CMSNT = new DB();
        $getUser = $CMSNT->get_row("SELECT * FROM `users` WHERE `token` = '" . check_string($_COOKIE['token']) . "' ");

        $this->botToken = $CMSNT->site('telegram_forward_token');
        $this->chatId = $getUser['telegram_chat_id'];

        if (!$getUser['enable_telegram']) {
            $this->enabledTelegram = false;
        } else {
            $this->enabledTelegram = true;
        }
    }

    public function sendMessage($message, $parseMode = null)
    {
        if (!$this->enabledTelegram) {
            return false;
        }

        $client = new Client();
        $data = [
            'chat_id' => $this->chatId,
            'text' => $message
        ];

        if ($parseMode) {
            $data['parse_mode'] = $parseMode;
        }

        try {
            $response = $client->post("https://api.telegram.org/bot{$this->botToken}/sendMessage", [
                'json' => $data
            ]);

            if ($response->getStatusCode() == 200) {
                return true;
            } else {
                error_log("Telegram API error: " . $response->getBody()->getContents());
                return false;
            }
        } catch (\Exception $e) {
            // Xử lý và log lỗi chi tiết
            error_log("Error sending Telegram message: " . $e->getMessage());
            return false;
        }
    }

    public function send_live($lista, $auth_or_charge = 'AUTH')
    {
        $message = "*Live* | *Pepechk.cc* | `$lista` | `$auth_or_charge`";
        return $this->sendMessage($message, 'Markdown');
    }

    public function send_ccn($lista, $msg)
    {
        $message = "*CCN* | *Pepechk.cc* | `$lista` | `$msg`";
        return $this->sendMessage($message, 'Markdown');
    }

    public function send_die($lista, $msg)
    {
        $message = "*DIE* | *Pepechk.cc* | `$lista` | `$msg`";
        return $this->sendMessage($message, 'Markdown');
    }

    public function send_err($msg)
    {
        $message = "*ERROR* | *Pepechk.cc* | `$msg`";
        return $this->sendMessage($message, 'Markdown');
    }
}
