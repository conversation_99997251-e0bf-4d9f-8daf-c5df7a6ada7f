<?php
require 'vendor/autoload.php'; // <PERSON><PERSON><PERSON> bảo đúng đường dẫn đến autoload.php

use GuzzleHttp\Client;

class RandomUser
{
    private $client;

    public function __construct()
    {
        $this->client = new Client();
    }

    /**
     * L<PERSON>y dữ liệu người dùng ngẫu nhiên từ randomuser.me API
     *
     * @return array|null
     */
    public function getUserFromUS()
    {
        try {
            $url = 'https://randomuser.me/api?nat=us'; // API của randomuser.me để lấy người dùng US
            $response = $this->client->request('GET', $url);

            // Kiểm tra xem yêu cầu thành công hay không
            if ($response->getStatusCode() == 200) {
                $data = json_decode($response->getBody(), true);

                // Trích xuất thông tin người dùng
                $firstname = $data['results'][0]['name']['first'];   // Gán firstname
                $lastname = $data['results'][0]['name']['last'];     // <PERSON><PERSON> lastname
                $email = $this->generateRandomEmail($firstname, $lastname); // Tạo email ngẫu nhiên

                // Lấy địa chỉ
                $street = $data['results'][0]['location']['street']['number'] . ' ' . $data['results'][0]['location']['street']['name'];
                $city = $data['results'][0]['location']['city'];
                $postcode = substr($data['results'][0]['location']['postcode'], 0, 5); // Chỉ lấy 5 số đầu tiên của postcode
                $stateFullName = $data['results'][0]['location']['state'];
                $stateAbbreviation = $this->getStateAbbreviation($stateFullName);

                // Lấy số điện thoại
                $phone = $data['results'][0]['phone'];

                // Trả về tất cả thông tin dưới dạng mảng
                return [
                    'firstname' => $firstname,
                    'lastname' => $lastname,
                    'email' => $email,
                    'street' => $street,
                    'city' => $city,
                    'postcode' => $postcode,
                    'state_full' => $stateFullName,
                    'state_abbreviation' => $stateAbbreviation,
                    'phone' => $phone
                ];
            } else {
                // Trường hợp thất bại từ phía API
                throw new Exception('Failed to retrieve data from randomuser.me API');
            }
        } catch (Exception $e) {
            // Trả về lỗi với thông tin từ $e
            die(json_encode([
                'status' => 'unk',
                'message' => $e->getMessage()
            ]));
        }
    }

    /**
     * Chuyển đổi tên đầy đủ của tiểu bang sang viết tắt
     *
     * @param string $stateFullName
     * @return string|null
     */
    private function getStateAbbreviation($stateFullName)
    {
        $states = [
            'Alabama' => 'AL',
            'Alaska' => 'AK',
            'Arizona' => 'AZ',
            'Arkansas' => 'AR',
            'California' => 'CA',
            'Colorado' => 'CO',
            'Connecticut' => 'CT',
            'Delaware' => 'DE',
            'Florida' => 'FL',
            'Georgia' => 'GA',
            'Hawaii' => 'HI',
            'Idaho' => 'ID',
            'Illinois' => 'IL',
            'Indiana' => 'IN',
            'Iowa' => 'IA',
            'Kansas' => 'KS',
            'Kentucky' => 'KY',
            'Louisiana' => 'LA',
            'Maine' => 'ME',
            'Maryland' => 'MD',
            'Massachusetts' => 'MA',
            'Michigan' => 'MI',
            'Minnesota' => 'MN',
            'Mississippi' => 'MS',
            'Missouri' => 'MO',
            'Montana' => 'MT',
            'Nebraska' => 'NE',
            'Nevada' => 'NV',
            'New Hampshire' => 'NH',
            'New Jersey' => 'NJ',
            'New Mexico' => 'NM',
            'New York' => 'NY',
            'North Carolina' => 'NC',
            'North Dakota' => 'ND',
            'Ohio' => 'OH',
            'Oklahoma' => 'OK',
            'Oregon' => 'OR',
            'Pennsylvania' => 'PA',
            'Rhode Island' => 'RI',
            'South Carolina' => 'SC',
            'South Dakota' => 'SD',
            'Tennessee' => 'TN',
            'Texas' => 'TX',
            'Utah' => 'UT',
            'Vermont' => 'VT',
            'Virginia' => 'VA',
            'Washington' => 'WA',
            'West Virginia' => 'WV',
            'Wisconsin' => 'WI',
            'Wyoming' => 'WY',
        ];

        return $states[$stateFullName] ?? null; // Trả về viết tắt hoặc null nếu không tìm thấy
    }

    /**
     * Tạo email ngẫu nhiên từ họ và tên
     *
     * @param string $firstName
     * @param string $lastName
     * @return string
     */
    private function generateRandomEmail($firstName, $lastName)
    {
        // Các domain email phổ biến
        $domains = ['gmail.com', 'hotmail.com', 'yahoo.com', 'outlook.com'];
        // Random domain
        $domain = $domains[array_rand($domains)];
        // Tạo email ngẫu nhiên từ họ, tên và domain
        $email = strtolower($firstName . '.' . $lastName . rand(10000, 99999) . '@' . $domain);

        return $email;
    }
}
