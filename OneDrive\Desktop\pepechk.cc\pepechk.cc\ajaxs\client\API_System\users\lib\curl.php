<?php

/*
----------------------------------------------
| UPDATE TIME | LOG             | AUTHOR     |
| 9/21/2024   | Update new API  | Juldeptrai |
----------------------------------------------
*/

require_once 'user_agent.php';




class CurlX
{
    const VERSION = '0.1.0';
    private static array $default = [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HEADER => false,
        CURLINFO_HEADER_OUT => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_AUTOREFERER => true,
        CURLOPT_CONNECTTIMEOUT => 30,
        CURLOPT_TIMEOUT => 60,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => 0,
        CURLOPT_ENCODING => '', // Hỗ trợ nén nội dung (gzip, deflate)
        CURLOPT_COOKIESESSION => true,
        CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2 // Bắt buộc dùng TLS 1.2 trở lên
    ];

    private static string $current_dir = '';
    
    private static $ch;
    
    private static array $info;
    private static $headersCallBack;
    
    private static string $cookie_file = '';
    private static string $ua = '';
    
    private static int $error_code;
    private static string $error_string;
    
    private static $response;

    /**
     * Initialize cURL with URL and default options
     *
     * @access private
     * @param string $url
     * @return void
     */
    private static function Prepare(string $url): void 
    {
        self::$ch = curl_init($url);
        self::SetOpt(self::$default);
    }

    /**
     * Set options for cURL
     *
     * @access public
     * @param array $option
     * @return void
     */
    public static function SetOpt(array $option): void 
    {
        curl_setopt_array(self::$ch, $option);
    }

    /**
     * Set custom headers for cURL
     *
     * @access private
     * @param array $header
     * @return void
     */
    private static function Header(array $header): void 
    {
        self::SetOpt([CURLOPT_HTTPHEADER => $header]);
    }

    /**
     * Set cookie file for cURL
     *
     * @access private
     * @param string $file
     * @return void
     */
    private static function SetCookie(string $file): void 
    {
        self::$current_dir = dirname(__FILE__);
        if (!is_dir(self::$current_dir . '/Cache/')) {
            mkdir(self::$current_dir . '/Cache/', 0755);
        }
        self::$cookie_file = sprintf("%s/Cache/curlX_%s.txt", self::$current_dir, $file);
        self::SetOpt([
            CURLOPT_COOKIEJAR => self::$cookie_file,
            CURLOPT_COOKIEFILE => self::$cookie_file
        ]);
    }

    /**
     * Delete cookie file for cURL
     *
     * @access public
     * @return void
     */
    public static function DeleteCookie(): void 
    {
        self::$current_dir = dirname(__FILE__) . '/Cache/';
        if (!is_dir(self::$current_dir) || !is_readable(self::$current_dir)) {
            trigger_error("Cache directory does not exist or is not readable.", E_USER_WARNING);
            return;
        }

        foreach (glob(self::$current_dir . 'curlX_*.txt') as $cookieFile) {
            if (is_file($cookieFile)) {
                chmod($cookieFile, 0644);
                unlink($cookieFile);
            }
        }
    }

    /**
     * Send GET request with headers, cookies, and server tunnel
     *
     * @access public
     * @param string $url
     * @param array $headers
     * @param string $cookie
     * @param array $server
     * @param string $ua
     * @return object
     */
    public static function Get(string $url, array $headers = NULL, string $cookie = NULL, array $server = NULL, string $ua = NULL): object 
    {
        self::Prepare($url);
        if ($ua) {
            self::SetOpt([CURLOPT_USERAGENT => $ua]);
        } else {
            $ua = self::UserAgent();
            self::SetOpt([CURLOPT_USERAGENT => $ua]);
        }
        self::CheckParam($headers, $cookie, $server);
        return self::Run();
    }

    /**
     * Send POST request with custom data, headers, cookies, and server tunnel
     *
     * @access public
     * @param string $url
     * @param string|array $data
     * @param array $headers
     * @param string $cookie
     * @param array $server
     * @return object
     */
    public static function Post(string $url, $data = NULL, array $headers = NULL, string $cookie = NULL, array $server = NULL): object 
    {
        self::Prepare($url);
        $ua = self::UserAgent();
        self::SetOpt([
            CURLOPT_USERAGENT => $ua,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => self::DataType($data)
        ]);
        self::CheckParam($headers, $cookie, $server);
        return self::Run();
    }
    
    /**
     * Send a PATCH request method with custom data, headers, cookies, and server tunnel
     *
     * @access public
     * @param string $url
     * @param string|array $data
     * @param array $headers
     * @param string $cookie
     * @param array $server
     *
     * @return object
     */
    public static function Patch(string $url, $data = NULL, array $headers = NULL, string $cookie = NULL, array $server = NULL) : object
    {
        self::Prepare($url);
        self::SetOpt([
            CURLOPT_USERAGENT => self::userAgent(),
            CURLOPT_CUSTOMREQUEST => 'PATCH',
            CURLOPT_POSTFIELDS => self::DataType($data)
        ]);
        self::CheckParam($headers, $cookie, $server);
        return self::Run();
    }

    /**
     * Send cURL request
     *
     * @access public
     * @return object
     */
    public static function Run(): object 
    {
        self::MakeStdClass();
        self::SetOpt([CURLOPT_HEADERFUNCTION => createHeaderCallback(self::$headersCallBack)]);

        self::$response = curl_exec(self::$ch);
        self::$info = curl_getinfo(self::$ch);

        if (self::$response === false) {
            self::$error_code = curl_errno(self::$ch);
            self::$error_string = curl_error(self::$ch);
            curl_close(self::$ch);

            return (object) [
                'success' => false,
                'code' => self::$info['http_code'],
                'headers' => (object) [
                    'request' => key_exists('request_header', self::$info) ? self::parseHeadersHandle(self::$info['request_header']) : [],
                    'response' => self::parseHeadersHandle(self::$headersCallBack->rawResponseHeaders)
                ],
                'errno' => self::$error_code,
                'error' => self::$error_string
            ];
        } else {
            curl_close(self::$ch);

            return (object) [
                'success' => true,
                'code' => self::$info['http_code'],
                'headers' => (object) [
                    'request' => self::parseHeadersHandle(self::$info['request_header']),
                    'response' => self::parseHeadersHandle(self::$headersCallBack->rawResponseHeaders)
                ],
                'body' => self::$response
            ];
        }
    }

    /**
     * Random delay to mimic human behavior
     *
     * @access public
     * @param int $min
     * @param int $max
     */
    public static function RandomDelay(int $min = 500, int $max = 1500): void 
    {
        usleep(rand($min, $max) * 1000); // delay in milliseconds
    }

    /**
     * Generate User-Agent using the userAgent class
     *
     * @access private
     * @return string
     */
    private static function UserAgent(): string 
    {
        $userAgentGenerator = new userAgent();
        return $userAgentGenerator->generate();
    }

    /**
     * Check parameters for cURL (headers, cookies, etc.)
     *
     * @access private
     * @param array|null $headers
     * @param string|null $cookie
     * @param array|null $server
     * @return void
     */
    private static function CheckParam(array $headers = NULL, string $cookie = NULL, array $server = NULL): void 
    {
        if (!empty($headers)) {
            self::Header($headers);
        }

        if (!empty($cookie)) {
            self::SetCookie($cookie);
        }

        if (!empty($server)) {
            self::AutoRouter($server);
        }
    }

    /**
     * Auto routing and handling proxy servers
     *
     * @access private
     * @param array $args
     * @return void
     */
    private static function AutoRouter(array $args): void 
    {
        switch (strtoupper($args['METHOD'])) {
            case 'TUNNEL':
                self::Tunnel($args);
                break;
            case 'CUSTOM':
                self::proxyAuth($args);
                break;
        }
    }

    /**
     * Set tunnel for proxy
     *
     * @access private
     * @param array $args
     * @return void
     */
    private static function Tunnel(array $args): void 
    {
        self::SetOpt([
            CURLOPT_PROXY => $args['SERVER'],
            CURLOPT_HTTPPROXYTUNNEL => true
        ]);
    }

    /**
     * Set proxy authentication if required
     *
     * @access private
     * @param array $args
     * @return void
     */
    private static function proxyAuth(array $args): void 
    {
        self::SetOpt([
            CURLOPT_PROXY => $args['SERVER'],
            CURLOPT_PROXYUSERPWD => $args['AUTH']
        ]);
    }

    /**
     * Detect data type and convert to appropriate format
     *
     * @access private
     * @param string|array|object|null $data
     * @return string
     */
    private static function DataType($data): string 
    {
        if (empty($data)) {
            return '';
        } elseif (is_array($data) || is_object($data)) {
            return json_encode($data);
        } else {
            return $data;
        }
    }
    
    public static function ParseString(string $str, string $start, string $end, bool $decode=false) : string 
    {   
        return $decode ? base64_decode(explode($end, explode($start, $str)[1])[0]) : explode($end, explode($start, $str)[1])[0];
    }

    /**
     * Parse headers from cURL
     *
     * @access private
     * @param string $raw
     * @return array
     */
    private static function parseHeaders(string $raw): array 
    {
        $raw = preg_split('/\r\n/', $raw, -1, PREG_SPLIT_NO_EMPTY);
        $http_headers = [];

        for ($i = 1; $i < count($raw); $i++) {
            if (strpos($raw[$i], ':') !== false) {
                list($key, $value) = explode(':', $raw[$i], 2);
                $key = trim($key);
                $value = trim($value);
                isset($http_headers[$key]) ? $http_headers[$key] .= ',' . $value : $http_headers[$key] = $value;
            }
        }

        return [$raw[0], $http_headers];
    }

    /**
     * Parse and handle headers in request
     *
     * @access private
     * @param string $raw
     * @return array
     */
    private static function parseHeadersHandle($raw): array 
    {
        if (empty($raw))
            return [];

        list($scheme, $headers) = self::parseHeaders($raw);
        $request_headers['scheme'] = $scheme;
        unset($headers['request_header']);
        
        foreach ($headers as $key => $value) {
            $request_headers[$key] = $value;
        }

        return $request_headers;
    }

    /**
     * Create a placeholder to temporarily store the header callback data.
     * 
     * @access private
     * @return void
     */
    private static function MakeStdClass(): void
    {
        $hcd = new \stdClass();
        $hcd->rawResponseHeaders = '';
        self::$headersCallBack = $hcd;
    }
}

/**
 * CreateHeaderCallback for handling headers
 */
function createHeaderCallback($headersCallBack) {
    return function ($_, $header) use ($headersCallBack) {
        $headersCallBack->rawResponseHeaders .= $header;
        return strlen($header);
    };
}
?>