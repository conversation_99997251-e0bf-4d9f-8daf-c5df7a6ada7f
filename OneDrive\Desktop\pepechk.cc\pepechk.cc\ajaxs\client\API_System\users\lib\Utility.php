<?php

class Utility
{
    private $deviceSessionId;
    private $correlationId;

    public function __construct()
    {
        // Tạo device_session_id và correlation_id
        $this->deviceSessionId = md5(uniqid(rand(), true));
        $this->correlationId = md5(uniqid(rand(), true));
    }

    /**
     * Hàm lấy chuỗi con giữa 2 chuỗi.
     * 
     * @param string $string Chuỗi chính
     * @param string $start Chuỗi bắt đầu
     * @param string $end Chuỗi kết thúc
     * @return string Chuỗi con tìm được
     */
    public function getStr($string, $start, $end)
    {
        $string = trim(preg_replace('/\s\s+/', ' ', $string));
        $str = explode($start, $string);
        $str = explode($end, $str[1]);
        return $str[0];
    }

    /**
     * Hàm tạo GUID
     * 
     * @return string GUID đã được tạo
     */
    public function createGuid()
    {
        if (function_exists('com_create_guid')) {
            return strtolower(trim(com_create_guid(), '{}'));
        } else {
            mt_srand((double)microtime() * 10000);
            $charid = md5(uniqid(rand(), true));
            $hyphen = chr(45); // Ký tự "-"
            $uuid = substr($charid, 0, 8) . $hyphen
                . substr($charid, 8, 4) . $hyphen
                . substr($charid, 12, 4) . $hyphen
                . substr($charid, 16, 4) . $hyphen
                . substr($charid, 20, 12);
            return $uuid;
        }
    }

    /**
     * Hàm tạo mật khẩu bảo mật ngẫu nhiên.
     * 
     * @param int $length Độ dài mật khẩu
     * @return string Mật khẩu đã được tạo
     */
    public function generateSecurePassword($length = 12)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()_-=+;:,<.>?';
        $charactersLength = strlen($characters);
        $password = '';
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, $charactersLength - 1)];
        }
        return $password;
    }

    /**
     * Hàm tạo tên người dùng ngẫu nhiên.
     * 
     * @param int $length Độ dài tên người dùng
     * @return string Tên người dùng đã được tạo
     */
    public function randomUsername($length = 8)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $username = '';
        for ($i = 0; $i < $length; $i++) {
            $username .= $characters[rand(0, $charactersLength - 1)];
        }
        return $username;
    }

    /**
     * Lấy device session id
     * 
     * @return string deviceSessionId
     */
    public function getDeviceSessionId()
    {
        return $this->deviceSessionId;
    }

    /**
     * Lấy correlation id
     * 
     * @return string correlationId
     */
    public function getCorrelationId()
    {
        return $this->correlationId;
    }
}

?>
