<?php

class _chung
{
    public function xulythe($lista)
    {
        $randomzip = rand(00000, 99999);
        $lista = str_replace('/', '|', $lista);
        $lista = trim(str_replace(array("\\\"", "\\'"), array("\"", "'"), $lista));
        $lista = str_replace("\r\r", "\r", $lista);
        $lista = str_replace("\n\n", "\n", $lista);

        // Xử lý thông tin thẻ
        $in_fo = $this->info($lista);

        // Gán ZIP ngẫu nhiên nếu không có zip
        if (empty($in_fo['zip'])) {
            $in_fo['zip'] = '' . $randomzip . '';
        }

        // Gán CVV mặc định nếu không có CVV
        if (empty($in_fo['cvv'])) {
            $in_fo['cvv'] = 000;
        }

        // Ki<PERSON>m tra nếu các trườ<PERSON> thiết yếu thiếu và trả về lỗi
        if (empty($in_fo['num']) || empty($in_fo['mon']) || empty($in_fo['year'])) {
            die(json_encode(['status' => 'unk', 'message' => 'Please, Check your card information.']));
        }

        return [
            'n' => $in_fo['num'],
            'm' => $in_fo['mon'],
            'y' => $in_fo['year'],
            'c' => $in_fo['cvv'],
            't' => $in_fo['type'],
            'z' => $in_fo['zip']
        ];
    }

    function info($ccline)
    {
        $xy = array("|", "\\", "/", " ", "//", "||", "|||");
        $sepe = $xy[0];
        foreach ($xy as $v) {
            if (substr_count($ccline, $sepe) < substr_count($ccline, $v)) $sepe = $v;
        }
        $x = explode($sepe, $ccline);
        $ccnum = [];
        $ccv = [];

        // Kiểm tra từng phần của thông tin thẻ
        foreach ($x as $xx) {
            $xx = trim($xx);
            if (is_numeric($xx)) {
                $yy = strlen($xx);
                switch ($yy) {
                    case 15:
                        // Thẻ Amex có số thẻ 15 chữ số
                        if (substr($xx, 0, 1) == 3) {
                            $ccnum['num'] = $xx;
                            $ccnum['type'] = "Amex";
                        }
                        break;
                    case 16:
                        // Thẻ Visa, Mastercard, Discover có 16 chữ số
                        switch (substr($xx, 0, 1)) {
                            case '4':
                                $ccnum['num'] = $xx;
                                $ccnum['type'] = "visa";
                                break;
                            case '5':
                                $ccnum['num'] = $xx;
                                $ccnum['type'] = "mastercard";
                                break;
                            case '6':
                                $ccnum['num'] = $xx;
                                $ccnum['type'] = "discover";
                                break;
                        }
                        break;
                    case 1:
                        // Tháng dạng 1 chữ số, thêm '0' phía trước
                        if (($xx >= 1) && ($xx <= 12) && !isset($ccnum['mon'])) {
                            $ccnum['mon'] = '0' . $xx; // Tháng có 1 chữ số, thêm số 0 phía trước
                        }
                        break;
                    case 2:
                        // Tháng dạng 2 chữ số hoặc năm 2 chữ số
                        if (($xx >= 1) && ($xx <= 12) && !isset($ccnum['mon'])) {
                            $ccnum['mon'] = $xx; // Tháng
                        } elseif (($xx >= 9) && ($xx <= 99) && isset($ccnum['mon']) && !isset($ccnum['year'])) {
                            $ccnum['year'] = "20" . $xx; // Năm 2 chữ số
                        }
                        break;
                    case 4:
                        // Năm dạng 4 chữ số
                        if (($xx >= 2023) && ($xx <= 2099) && isset($ccnum['mon'])) {
                            $ccnum['year'] = $xx;
                        } else {
                            $ccv['cv4'] = $xx; // CVV 4 chữ số cho thẻ Amex
                        }
                        break;
                    case 3:
                        // CVV 3 chữ số cho Visa, Mastercard, Discover
                        $ccv['cv3'] = $xx;
                        break;
                    case 5:
                        // ZIP code
                        $ccnum['zip'] = $xx;
                        break;
                }
            }
        }

        // Kiểm tra và trả về thông tin thẻ
        if (isset($ccnum['num']) && isset($ccnum['mon']) && isset($ccnum['year'])) {
            if ($ccnum['type'] == "Amex") {
                $ccnum['cvv'] = isset($ccv['cv4']) ? $ccv['cv4'] : null;
            } else {
                $ccnum['cvv'] = isset($ccv['cv3']) ? $ccv['cv3'] : null;
            }

            return $ccnum;
        } else {
            return false;
        }
    }
}
